// routes/invoiceSyncRoutes.ts

import { Router } from 'express';
import { invoiceSyncController } from '../controller/invoiceSyncController';
import { quickbooksAuthMiddleware } from '../middleware/authMiddleware';
import {
    validateSyncSingleInvoice,
    validateSyncAllInvoices,
    validateGetInvoiceSyncStatus,
    validateGetAllInvoicesSyncStatus,
    validateRetryInvoiceSync
} from '../middleware/invoiceSyncValidation';

const invoiceSyncRoutes = Router();

// Apply QuickBooks auth middleware to all routes
invoiceSyncRoutes.use(quickbooksAuthMiddleware);

// ===== INVOICE SYNC ROUTES =====

/**
 * @route POST /api/v1/qbo/invoices/sync
 * @desc Sync all pending invoices to QuickBooks
 * @access Private
 * @headers Authorization: Bearer <access_token>
 * @headers realm-id: <quickbooks_realm_id>
 * @body limit - Optional limit for number of invoices to sync (max: 100)
 * @body forceSync - Optional flag to force sync even if already synced
 * @body includeAlreadySynced - Optional flag to include already synced invoices
 */
invoiceSyncRoutes.post(
    '/sync',
    validateSyncAllInvoices,
    invoiceSyncController.syncAllInvoices
);

invoiceSyncRoutes.post(
    '/sync/:invoiceId',
    validateSyncSingleInvoice,
    invoiceSyncController.syncSingleInvoice
);

invoiceSyncRoutes.get(
    '/sync/status',
    validateGetAllInvoicesSyncStatus,
    invoiceSyncController.getAllInvoicesSyncStatus
);


invoiceSyncRoutes.get(
    '/sync/:invoiceId/status',
    validateGetInvoiceSyncStatus,
    invoiceSyncController.getInvoiceSyncStatus
);

invoiceSyncRoutes.get(
    '/', 
    invoiceSyncController.getInvoices);

invoiceSyncRoutes.get(
    '/:invoiceId', 
    invoiceSyncController.getInvoiceById);






export default invoiceSyncRoutes;