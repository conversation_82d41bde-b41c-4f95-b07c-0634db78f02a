// routes/invoiceSyncRoutes.ts

import { Router } from 'express';
import { invoiceSyncController } from '../controller/invoiceSyncController';
import { quickbooksAuthMiddleware } from '../middleware/authMiddleware';
import {
    validateSyncSingleInvoice,
    validateSyncAllInvoices,
    validateGetInvoiceSyncStatus,
    validateGetAllInvoicesSyncStatus,
    validateRetryInvoiceSync
} from '../middleware/invoiceSyncValidation';

const invoiceSyncRoutes = Router();

// Apply QuickBooks auth middleware to all routes
invoiceSyncRoutes.use(quickbooksAuthMiddleware);

// ===== INVOICE SYNC ROUTES =====

/**
 * @route POST /api/v1/qbo/invoices/sync
 * @desc Sync all pending invoices to QuickBooks
 * @access Private
 * @headers Authorization: Bearer <access_token>
 * @headers realm-id: <quickbooks_realm_id>
 * @body limit - Optional limit for number of invoices to sync (max: 100)
 * @body forceSync - Optional flag to force sync even if already synced
 * @body includeAlreadySynced - Optional flag to include already synced invoices
 */
invoiceSyncRoutes.post(
    '/sync',
    validateSyncAllInvoices,
    invoiceSyncController.syncAllInvoices
);

/**
 * @route POST /api/v1/qbo/invoices/sync/:invoiceId
 * @desc Sync a single invoice to QuickBooks
 * @access Private
 * @headers Authorization: Bearer <access_token>
 * @headers realm-id: <quickbooks_realm_id>
 * @param invoiceId - ID of the invoice to sync
 */
invoiceSyncRoutes.post(
    '/sync/:invoiceId',
    validateSyncSingleInvoice,
    invoiceSyncController.syncSingleInvoice
);

// ===== SYNC STATUS ROUTES =====

/**
 * @route GET /api/v1/qbo/invoices/sync/status
 * @desc Get sync status of all invoices with filtering and pagination
 * @access Private
 * @headers Authorization: Bearer <access_token>
 * @headers realm-id: <quickbooks_realm_id>
 * @query page - Page number (default: 1)
 * @query limit - Number of records per page (default: 50, max: 100)
 * @query syncStatus - Filter by sync status (PENDING, SUCCESS, FAILED, etc.)
 * @query status - Filter by invoice status (DRAFT, SENT, PAID, etc.)
 * @query dateFrom - Filter invoices created from this date (ISO 8601)
 * @query dateTo - Filter invoices created up to this date (ISO 8601)
 */
invoiceSyncRoutes.get(
    '/sync/status',
    validateGetAllInvoicesSyncStatus,
    invoiceSyncController.getAllInvoicesSyncStatus
);

/**
 * @route GET /api/v1/qbo/invoices/sync/:invoiceId/status
 * @desc Get detailed sync status and history for a specific invoice
 * @access Private
 * @headers Authorization: Bearer <access_token>
 * @headers realm-id: <quickbooks_realm_id>
 * @param invoiceId - ID of the invoice to get status for
 */
invoiceSyncRoutes.get(
    '/sync/:invoiceId/status',
    validateGetInvoiceSyncStatus,
    invoiceSyncController.getInvoiceSyncStatus
);

// ===== ADVANCED SYNC ROUTES =====

/**
 * @route POST /api/v1/qbo/invoices/sync/:invoiceId/retry
 * @desc Retry syncing a failed invoice
 * @access Private
 * @headers Authorization: Bearer <access_token>
 * @headers realm-id: <quickbooks_realm_id>
 * @param invoiceId - ID of the invoice to retry
 * @body forceRetry - Optional flag to force retry regardless of status
 */
invoiceSyncRoutes.post(
    '/sync/:invoiceId/retry',
    validateRetryInvoiceSync,
    invoiceSyncController.retryInvoiceSync
);

/**
 * @route POST /api/v1/qbo/invoices/sync/bulk
 * @desc Sync multiple specific invoices to QuickBooks
 * @access Private
 * @headers Authorization: Bearer <access_token>
 * @headers realm-id: <quickbooks_realm_id>
 * @body invoiceIds - Array of invoice IDs to sync (max: 50)
 * @body forceSync - Optional flag to force sync even if already synced
 * @body stopOnError - Optional flag to stop processing on first error
 */
invoiceSyncRoutes.post(
    '/sync/bulk',
    async (req, res) => {
        // Bulk sync feature - to be implemented later
        return res.status(501).json({
            status: 'error',
            message: 'Bulk sync feature coming soon',
            data: null
        });
    }
);

// ===== UTILITY ROUTES =====

/**
 * @route GET /api/v1/qbo/invoices/sync/statistics
 * @desc Get overall sync statistics
 * @access Private
 * @headers Authorization: Bearer <access_token>
 * @headers realm-id: <quickbooks_realm_id>
 */
invoiceSyncRoutes.get('/sync/statistics', invoiceSyncController.getSyncStatistics);

/**
 * @route GET /api/v1/qbo/invoices/sync/health
 * @desc Health check for sync service
 * @access Private
 * @headers Authorization: Bearer <access_token>
 * @headers realm-id: <quickbooks_realm_id>
 */
invoiceSyncRoutes.get('/sync/health', async (req, res) => {
    try {
        const { realmId } = req.qbAuth!;
        
        // Basic health check - just verify we can access the database
        const { prisma } = await import('../config/db');
        
        // Check connection exists
        const connection = await prisma.qBOConnection.findUnique({
            where: { realmId }
        });

        if (!connection) {
            return res.status(404).json({
                status: 'error',
                message: 'QuickBooks connection not found',
                data: { 
                    realmId,
                    healthy: false,
                    timestamp: new Date().toISOString()
                }
            });
        }

        // Check if connection is active
        const isHealthy = connection.isConnected && new Date() < connection.expiresAt;

        return res.status(isHealthy ? 200 : 503).json({
            status: isHealthy ? 'success' : 'warning',
            message: isHealthy ? 'Sync service is healthy' : 'Sync service has connectivity issues',
            data: {
                realmId,
                healthy: isHealthy,
                connectionStatus: connection.isConnected ? 'connected' : 'disconnected',
                tokenExpired: new Date() >= connection.expiresAt,
                expiresAt: connection.expiresAt,
                lastSyncAt: connection.lastSyncAt,
                timestamp: new Date().toISOString()
            }
        });

    } catch (error) {
        console.error('Health check error:', error);
        return res.status(500).json({
            status: 'error',
            message: 'Health check failed',
            data: { 
                healthy: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString()
            }
        });
    }
});

export default invoiceSyncRoutes;