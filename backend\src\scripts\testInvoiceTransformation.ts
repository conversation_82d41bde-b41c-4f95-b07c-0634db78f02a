// Test script to verify invoice transformation fixes
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Mock invoice data that matches our database structure
const mockInvoice = {
    id: 'test-invoice-id',
    qboInvoiceId: null,
    customerId: '1', // QBO Customer ID
    invoiceDate: new Date('2025-07-11'),
    dueDate: new Date('2025-07-29'),
    store: 'Store 3',
    billingAddress: '4581 Finch St., , US, , ',
    docNumber: 'INV-0001',
    subtotal: 12.00,
    total: 12.96,
    syncToken: null,
    sparse: null,
    sendLater: false,
    status: 'DRAFT' as const,
    lineItems: [
        {
            itemRef: '17',
            itemName: 'Sprinkler Pipes',
            description: 'Sprinkler Pipes',
            quantity: 3,
            unitPrice: 4,
            amount: 12,
            detailType: 'SalesItemLineDetail'
        },
        {
            itemRef: null,
            itemName: 'Tax',
            description: 'Sales Tax',
            quantity: 1,
            unitPrice: 0.96,
            amount: 0.96,
            detailType: 'TaxLineDetail'
        }
    ],
    qboConnectionId: 'test-connection-id',
    lastSyncedAt: null,
    syncStatus: 'PENDING' as const,
    createdAt: new Date(),
    updatedAt: new Date(),
    customer: {
        id: '1',
        displayName: 'Test Customer',
        email: '<EMAIL>'
    }
};

// Import the transformation functions (we'll need to export them from the service)
// For now, let's recreate the logic here to test

interface QBOInvoiceLineItem {
    Amount: number;
    DetailType: string;
    SalesItemLineDetail?: {
        ItemRef: {
            value: string;
            name?: string;
        };
        Qty?: number;
        UnitPrice?: number;
    };
    Description?: string;
}

interface QBOInvoicePayload {
    CustomerRef: {
        value: string;
    };
    Line: QBOInvoiceLineItem[];
    DocNumber?: string;
    TxnDate?: string;
    DueDate?: string;
    BillAddr?: {
        Line1?: string;
        City?: string;
        Country?: string;
        CountrySubDivisionCode?: string;
        PostalCode?: string;
    };
    ShipAddr?: {
        Line1?: string;
        City?: string;
        Country?: string;
        CountrySubDivisionCode?: string;
        PostalCode?: string;
    };
    SalesTermRef?: {
        value: string;
    };
    PrivateNote?: string;
    TotalAmt?: number;
}

const transformLineItemsToQBO = (lineItems: any[]): QBOInvoiceLineItem[] => {
    const qboLineItems: QBOInvoiceLineItem[] = [];

    for (const item of lineItems) {
        if (item.detailType === 'SalesItemLineDetail') {
            // Only include essential fields that QBO expects
            qboLineItems.push({
                DetailType: 'SalesItemLineDetail',
                Amount: item.amount,
                SalesItemLineDetail: {
                    ItemRef: {
                        value: item.itemRef,
                        name: item.itemName
                    }
                }
            });
        }
        // Skip tax line items for now as they might be causing issues
        // QBO can calculate taxes automatically based on customer settings
    }

    return qboLineItems;
};

const transformInvoiceToQBO = async (invoice: any): Promise<QBOInvoicePayload> => {
    const lineItems = Array.isArray(invoice.lineItems) ? invoice.lineItems : [];
    
    // Create a minimal payload that matches QBO's expected format
    const qboPayload: QBOInvoicePayload = {
        Line: transformLineItemsToQBO(lineItems),
        CustomerRef: {
            value: invoice.customerId
        }
    };

    return qboPayload;
};

async function testInvoiceTransformation() {
    console.log('🧪 Testing Invoice Transformation...\n');
    
    console.log('📋 Original Invoice Data:');
    console.log(JSON.stringify(mockInvoice, null, 2));
    
    console.log('\n🔄 Transforming to QBO format...');
    const qboPayload = await transformInvoiceToQBO(mockInvoice);
    
    console.log('\n📤 QBO Payload (NEW FORMAT):');
    console.log(JSON.stringify(qboPayload, null, 2));
    
    console.log('\n📤 Expected QBO Format (from working example):');
    const expectedFormat = {
        "Line": [
            {
                "DetailType": "SalesItemLineDetail", 
                "Amount": 100.0, 
                "SalesItemLineDetail": {
                    "ItemRef": {
                        "name": "Services", 
                        "value": "1"
                    }
                }
            }
        ], 
        "CustomerRef": {
            "value": "1"
        }
    };
    console.log(JSON.stringify(expectedFormat, null, 2));
    
    console.log('\n✅ Transformation completed!');
    console.log('\n🔍 Key Changes Made:');
    console.log('1. Removed optional fields (DocNumber, TxnDate, DueDate, BillAddr, PrivateNote, TotalAmt)');
    console.log('2. Simplified line item structure - removed Qty and UnitPrice from SalesItemLineDetail');
    console.log('3. Removed tax line items (QBO can calculate taxes automatically)');
    console.log('4. Reordered fields to match QBO expected format (Line first, then CustomerRef)');
    
    console.log('\n📊 Payload Comparison:');
    console.log(`- Line items: ${qboPayload.Line.length} (was ${mockInvoice.lineItems.length} including tax)`);
    console.log(`- Customer ID: ${qboPayload.CustomerRef.value}`);
    console.log(`- Total fields: ${Object.keys(qboPayload).length} (reduced from many optional fields)`);
}

// Run the test
testInvoiceTransformation()
    .then(() => {
        console.log('\n🎉 Test completed successfully!');
        process.exit(0);
    })
    .catch((error) => {
        console.error('\n❌ Test failed:', error);
        process.exit(1);
    });
