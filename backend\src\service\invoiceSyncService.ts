// services/invoiceSyncService.ts

import axios from 'axios';
import { prisma } from '../config/db';
import { Invoice, SyncStatus, SyncOperation, TransactionType } from '@prisma/client';

// Helper function to get API base URL
const getQboApiBaseUrl = (): string => {
    return process.env.ENVIRONMENT === 'production'
        ? 'https://quickbooks.api.intuit.com'
        : 'https://sandbox-quickbooks.api.intuit.com';
};

// QuickBooks Invoice Line Item interface
interface QBOInvoiceLineItem {
    Amount: number;
    DetailType: string;
    SalesItemLineDetail?: {
        ItemRef: {
            value: string;
            name?: string;
        };
        Qty?: number;
        UnitPrice?: number;
    };
    Description?: string;
}

// QuickBooks Invoice Payload interface
interface QBOInvoicePayload {
    CustomerRef: {
        value: string;
    };
    Line: QBOInvoiceLineItem[];
    DocNumber?: string;
    TxnDate?: string;
    DueDate?: string;
    BillAddr?: {
        Line1?: string;
        City?: string;
        Country?: string;
        CountrySubDivisionCode?: string;
        PostalCode?: string;
    };
    ShipAddr?: {
        Line1?: string;
        City?: string;
        Country?: string;
        CountrySubDivisionCode?: string;
        PostalCode?: string;
    };
    SalesTermRef?: {
        value: string;
    };
    PrivateNote?: string;
    TotalAmt?: number;
}

// QuickBooks Invoice Response interface
interface QBOInvoiceResponse {
    QueryResponse?: any;
    Invoice?: {
        Id: string;
        DocNumber: string;
        SyncToken: string;
        MetaData: {
            CreateTime: string;
            LastUpdatedTime: string;
        };
        TotalAmt: number;
        Balance: number;
        [key: string]: any;
    };
    Fault?: {
        Error: Array<{
            Detail: string;
            code: string;
            element?: string;
        }>;
        type: string;
    };
}

// Response interfaces for service methods
interface InvoiceSyncResult {
    success: boolean;
    qboInvoiceId?: string;
    syncToken?: string;
    message: string;
    error?: string;
}

interface BatchSyncResult {
    success: boolean;
    totalProcessed: number;
    successCount: number;
    failureCount: number;
    results: Array<{
        invoiceId: string;
        docNumber: string;
        success: boolean;
        qboInvoiceId?: string;
        message: string;
        error?: string;
    }>;
    message: string;
}

interface InvoiceSyncStatusResult {
    invoice: any;
    syncLogs: any[];
    lastSync?: Date;
    status: SyncStatus;
}

interface InvoicesSyncStatusResult {
    invoices: any[];
    totalCount: number;
    totalPages: number;
    currentPage: number;
    summary: {
        totalInvoices: number;
        syncedInvoices: number;
        pendingInvoices: number;
        failedInvoices: number;
    };
}

/**
 * Find or create QBO connection based on realmId
 */
const findOrCreateConnection = async (realmId: string, accessToken: string): Promise<string> => {
    try {
        let connection = await prisma.qBOConnection.findUnique({
            where: { realmId }
        });

        if (!connection) {
            console.log(`Creating new QBO connection for realmId: ${realmId}`);
            connection = await prisma.qBOConnection.create({
                data: {
                    realmId,
                    accessToken,
                    refreshToken: '',
                    expiresAt: new Date(Date.now() + 3600 * 1000),
                    refreshExpiresAt: new Date(Date.now() + 101 * 24 * 3600 * 1000),
                    isConnected: true,
                    companyName: `Company-${realmId}`,
                    connectedAt: new Date()
                }
            });
        }

        return connection.id;
    } catch (error) {
        console.error('Error finding/creating QBO connection:', error);
        throw new Error(`Failed to find or create QBO connection: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};

/**
 * Create sync log entry
 */
const createSyncLog = async (data: {
    transactionType: TransactionType;
    systemTransactionId: string;
    quickbooksId?: string;
    status: SyncStatus;
    operation: SyncOperation;
    qboConnectionId: string;
    invoiceId?: string;
    requestPayload?: any;
    responsePayload?: any;
    errorMessage?: string;
    errorCode?: string;
}): Promise<void> => {
    try {
        await prisma.syncLog.create({
            data: {
                transactionType: data.transactionType,
                systemTransactionId: data.systemTransactionId,
                quickbooksId: data.quickbooksId,
                status: data.status,
                operation: data.operation,
                qboConnectionId: data.qboConnectionId,
                invoiceId: data.invoiceId,
                requestPayload: data.requestPayload,
                responsePayload: data.responsePayload,
                errorMessage: data.errorMessage,
                errorCode: data.errorCode,
                startedAt: new Date(),
                completedAt: data.status !== 'IN_PROGRESS' ? new Date() : undefined
            }
        });
    } catch (error) {
        console.error('Error creating sync log:', error);
    }
};

/**
 * Transform our invoice line items to QuickBooks format
 */
const transformLineItemsToQBO = (lineItems: any[]): QBOInvoiceLineItem[] => {
    const qboLineItems: QBOInvoiceLineItem[] = [];

    for (const item of lineItems) {
        if (item.detailType === 'SalesItemLineDetail') {
            // Only include essential fields that QBO expects
            qboLineItems.push({
                DetailType: 'SalesItemLineDetail',
                Amount: item.amount,
                SalesItemLineDetail: {
                    ItemRef: {
                        value: item.itemRef,
                        name: item.itemName
                    }
                }
            });
        }
        // Skip tax line items for now as they might be causing issues
        // QBO can calculate taxes automatically based on customer settings
    }

    return qboLineItems;
};

/**
 * Parse billing address into QuickBooks format
 */
const parseBillingAddress = (billingAddress?: string) => {
    if (!billingAddress) return undefined;

    const parts = billingAddress.split(',').map(part => part.trim());
    
    return {
        Line1: parts[0] || '',
        City: parts[1] || '',
        Country: 'US',
        CountrySubDivisionCode: parts[2] || '',
        PostalCode: parts[3] || ''
    };
};

/**
 * Transform our invoice to QuickBooks format
 */
const transformInvoiceToQBO = async (invoice: Invoice & { customer: any }): Promise<QBOInvoicePayload> => {
    const lineItems = Array.isArray(invoice.lineItems) ? invoice.lineItems : [];

    // Create a minimal payload that matches QBO's expected format
    const qboPayload: QBOInvoicePayload = {
        Line: transformLineItemsToQBO(lineItems),
        CustomerRef: {
            value: invoice.customerId
        }
    };

    return qboPayload;
};

/**
 * Sync single invoice to QuickBooks
 */
const syncInvoiceToQBO = async (
    invoiceId: string,
    accessToken: string,
    realmId: string
): Promise<InvoiceSyncResult> => {
    try {
        // Get QBO connection
        const qboConnectionId = await findOrCreateConnection(realmId, accessToken);

        // Fetch invoice with customer details
        const invoice = await prisma.invoice.findUnique({
            where: { id: invoiceId },
            include: {
                customer: true
            }
        });

        if (!invoice) {
            throw new Error(`Invoice with ID ${invoiceId} not found`);
        }

        if (invoice.qboInvoiceId) {
            return {
                success: false,
                message: `Invoice ${invoice.docNumber} is already synced to QuickBooks (ID: ${invoice.qboInvoiceId})`,
                error: 'ALREADY_SYNCED'
            };
        }

        // Transform invoice to QuickBooks format
        const qboPayload = await transformInvoiceToQBO(invoice);

        console.log(`Syncing invoice ${invoice.docNumber} to QuickBooks...`);

        // Update sync status to IN_PROGRESS
        await prisma.invoice.update({
            where: { id: invoiceId },
            data: { syncStatus: 'IN_PROGRESS' }
        });

        // Create initial sync log
        await createSyncLog({
            transactionType: 'INVOICE',
            systemTransactionId: invoiceId,
            status: 'IN_PROGRESS',
            operation: 'CREATE',
            qboConnectionId,
            invoiceId,
            requestPayload: qboPayload
        });

        // Make API call to QuickBooks
        const baseUrl = getQboApiBaseUrl();
        const response = await axios.post<QBOInvoiceResponse>(
            `${baseUrl}/v3/company/${realmId}/invoice`,
            qboPayload,
            {
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            }
        );

        // Check for QuickBooks errors
        if (response.data.Fault) {
            const error = response.data.Fault.Error[0];
            throw new Error(`QuickBooks API Error: ${error.Detail} (Code: ${error.code})`);
        }

        const qboInvoice = response.data.Invoice;
        if (!qboInvoice) {
            throw new Error('No invoice data returned from QuickBooks');
        }

        // Update our invoice with QuickBooks ID
        await prisma.invoice.update({
            where: { id: invoiceId },
            data: {
                qboInvoiceId: qboInvoice.Id,
                syncToken: qboInvoice.SyncToken,
                syncStatus: 'SUCCESS',
                lastSyncedAt: new Date()
            }
        });

        // Create success sync log
        await createSyncLog({
            transactionType: 'INVOICE',
            systemTransactionId: invoiceId,
            quickbooksId: qboInvoice.Id,
            status: 'SUCCESS',
            operation: 'CREATE',
            qboConnectionId,
            invoiceId,
            requestPayload: qboPayload,
            responsePayload: response.data
        });

        console.log(`✅ Invoice ${invoice.docNumber} synced successfully. QBO ID: ${qboInvoice.Id}`);

        return {
            success: true,
            qboInvoiceId: qboInvoice.Id,
            syncToken: qboInvoice.SyncToken,
            message: `Invoice ${invoice.docNumber} synced successfully to QuickBooks`
        };

    } catch (error) {
        console.error(`❌ Error syncing invoice ${invoiceId}:`, error);

        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const errorCode = axios.isAxiosError(error) ? error.response?.status?.toString() : 'SYNC_ERROR';

        // Update invoice sync status to FAILED
        try {
            await prisma.invoice.update({
                where: { id: invoiceId },
                data: { 
                    syncStatus: 'FAILED',
                    lastSyncedAt: new Date()
                }
            });

            // Get connection for error log
            const qboConnectionId = await findOrCreateConnection(realmId, accessToken);

            // Create error sync log
            await createSyncLog({
                transactionType: 'INVOICE',
                systemTransactionId: invoiceId,
                status: 'FAILED',
                operation: 'CREATE',
                qboConnectionId,
                invoiceId,
                errorMessage,
                errorCode
            });
        } catch (logError) {
            console.error('Error updating invoice status or creating error log:', logError);
        }

        return {
            success: false,
            message: `Failed to sync invoice: ${errorMessage}`,
            error: errorMessage
        };
    }
};

/**
 * Sync all pending invoices to QuickBooks
 */
const syncAllInvoicesToQBO = async (
    accessToken: string,
    realmId: string,
    limit?: number
): Promise<BatchSyncResult> => {
    try {
        // Get QBO connection
        const qboConnectionId = await findOrCreateConnection(realmId, accessToken);

        // Fetch pending invoices for this connection
        const pendingInvoices = await prisma.invoice.findMany({
            where: {
                qboConnectionId,
                syncStatus: 'PENDING',
                qboInvoiceId: null // Only sync invoices that haven't been synced yet
            },
            include: {
                customer: true
            },
            take: limit || 50, // Default limit of 50
            orderBy: {
                createdAt: 'asc' // Sync oldest first
            }
        });

        if (pendingInvoices.length === 0) {
            return {
                success: true,
                totalProcessed: 0,
                successCount: 0,
                failureCount: 0,
                results: [],
                message: 'No pending invoices found to sync'
            };
        }

        console.log(`📋 Found ${pendingInvoices.length} pending invoices to sync`);

        const results = [];
        let successCount = 0;
        let failureCount = 0;

        // Process each invoice
        for (const invoice of pendingInvoices) {
            console.log(`📄 Processing invoice ${invoice.docNumber}...`);

            const syncResult = await syncInvoiceToQBO(invoice.id, accessToken, realmId);
            
            results.push({
                invoiceId: invoice.id,
                docNumber: invoice.docNumber || `Invoice-${invoice.id}`,
                success: syncResult.success,
                qboInvoiceId: syncResult.qboInvoiceId,
                message: syncResult.message,
                error: syncResult.error
            });

            if (syncResult.success) {
                successCount++;
            } else {
                failureCount++;
            }

            // Add small delay between requests to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 100));
        }

        const message = `Batch sync completed: ${successCount} successful, ${failureCount} failed out of ${pendingInvoices.length} invoices`;
        console.log(`📊 ${message}`);

        return {
            success: true,
            totalProcessed: pendingInvoices.length,
            successCount,
            failureCount,
            results,
            message
        };

    } catch (error) {
        console.error('❌ Error in batch invoice sync:', error);
        throw new Error(`Batch invoice sync failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};

/**
 * Get invoice sync status
 */
const getInvoiceSyncStatus = async (invoiceId: string): Promise<InvoiceSyncStatusResult> => {
    try {
        const invoice = await prisma.invoice.findUnique({
            where: { id: invoiceId },
            include: {
                customer: {
                    select: {
                        id: true,
                        displayName: true,
                        email: true
                    }
                }
            }
        });

        if (!invoice) {
            throw new Error(`Invoice with ID ${invoiceId} not found`);
        }

        const syncLogs = await prisma.syncLog.findMany({
            where: {
                transactionType: 'INVOICE',
                systemTransactionId: invoiceId
            },
            orderBy: {
                timestamp: 'desc'
            },
            take: 10 // Last 10 sync attempts
        });

        return {
            invoice,
            syncLogs,
            lastSync: invoice.lastSyncedAt ?? undefined,
            status: invoice.syncStatus
        };

    } catch (error) {
        throw new Error(`Failed to get invoice sync status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};

/**
 * Get all invoices with their sync status (with pagination)
 */
const getAllInvoicesSyncStatus = async (options: {
    page: number;
    limit: number;
    syncStatus?: string;
    status?: string;
    realmId: string;
    dateFrom?: string;
    dateTo?: string;
}): Promise<InvoicesSyncStatusResult> => {
    try {
        const { page, limit, syncStatus, status, realmId, dateFrom, dateTo } = options;
        const skip = (page - 1) * limit;

        // Get QBO connection for this realm
        const connection = await prisma.qBOConnection.findUnique({
            where: { realmId }
        });

        if (!connection) {
            throw new Error('QuickBooks connection not found for this realm');
        }

        // Build where clause
        const where: any = {
            qboConnectionId: connection.id
        };

        if (syncStatus) {
            where.syncStatus = syncStatus;
        }

        if (status) {
            where.status = status;
        }

        if (dateFrom || dateTo) {
            where.createdAt = {};
            if (dateFrom) where.createdAt.gte = new Date(dateFrom);
            if (dateTo) where.createdAt.lte = new Date(dateTo);
        }

        // Get total count
        const totalCount = await prisma.invoice.count({ where });

        // Get invoices with pagination
        const invoices = await prisma.invoice.findMany({
            where,
            include: {
                customer: {
                    select: {
                        id: true,
                        displayName: true,
                        email: true
                    }
                }
            },
            orderBy: { createdAt: 'desc' },
            skip,
            take: limit
        });

        const totalPages = Math.ceil(totalCount / limit);

        // Calculate summary statistics
        const allInvoices = await prisma.invoice.findMany({
            where: { qboConnectionId: connection.id },
            select: {
                qboInvoiceId: true,
                syncStatus: true
            }
        });

        const summary = {
            totalInvoices: allInvoices.length,
            syncedInvoices: allInvoices.filter(inv => inv.qboInvoiceId).length,
            pendingInvoices: allInvoices.filter(inv => inv.syncStatus === 'PENDING').length,
            failedInvoices: allInvoices.filter(inv => inv.syncStatus === 'FAILED').length
        };

        return {
            invoices: invoices.map(invoice => ({
                id: invoice.id,
                docNumber: invoice.docNumber,
                total: invoice.total,
                status: invoice.status,
                qboInvoiceId: invoice.qboInvoiceId,
                syncStatus: invoice.syncStatus,
                lastSyncedAt: invoice.lastSyncedAt,
                customer: invoice.customer,
                createdAt: invoice.createdAt,
                isSynced: !!invoice.qboInvoiceId
            })),
            totalCount,
            totalPages,
            currentPage: page,
            summary
        };

    } catch (error) {
        throw new Error(`Failed to get invoices sync status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};

/**
 * Get sync statistics for a realm
 */
const getSyncStatistics = async (realmId: string): Promise<{
    invoices: {
        total: number;
        synced: number;
        pending: number;
        failed: number;
        syncedPercentage: string;
    };
    syncAttempts: {
        total: number;
        successful: number;
        failed: number;
        successRate: string;
    };
    lastSyncAt: Date | null;
}> => {
    try {
        // Get connection for this realm
        const connection = await prisma.qBOConnection.findUnique({
            where: { realmId }
        });

        if (!connection) {
            throw new Error('QuickBooks connection not found');
        }

        // Get invoice statistics
        const totalInvoices = await prisma.invoice.count({
            where: { qboConnectionId: connection.id }
        });

        const syncedInvoices = await prisma.invoice.count({
            where: {
                qboConnectionId: connection.id,
                qboInvoiceId: { not: null }
            }
        });

        const pendingInvoices = await prisma.invoice.count({
            where: {
                qboConnectionId: connection.id,
                syncStatus: 'PENDING'
            }
        });

        const failedInvoices = await prisma.invoice.count({
            where: {
                qboConnectionId: connection.id,
                syncStatus: 'FAILED'
            }
        });

        // Get sync logs statistics
        const totalSyncAttempts = await prisma.syncLog.count({
            where: {
                transactionType: 'INVOICE',
                qboConnectionId: connection.id
            }
        });

        const successfulSyncs = await prisma.syncLog.count({
            where: {
                transactionType: 'INVOICE',
                qboConnectionId: connection.id,
                status: 'SUCCESS'
            }
        });

        const failedSyncs = await prisma.syncLog.count({
            where: {
                transactionType: 'INVOICE',
                qboConnectionId: connection.id,
                status: 'FAILED'
            }
        });

        // Calculate success rate
        const successRate = totalSyncAttempts > 0 
            ? ((successfulSyncs / totalSyncAttempts) * 100).toFixed(2)
            : '0';

        return {
            invoices: {
                total: totalInvoices,
                synced: syncedInvoices,
                pending: pendingInvoices,
                failed: failedInvoices,
                syncedPercentage: totalInvoices > 0 
                    ? ((syncedInvoices / totalInvoices) * 100).toFixed(2)
                    : '0'
            },
            syncAttempts: {
                total: totalSyncAttempts,
                successful: successfulSyncs,
                failed: failedSyncs,
                successRate: successRate + '%'
            },
            lastSyncAt: connection.lastSyncAt
        };

    } catch (error) {
        throw new Error(`Failed to get sync statistics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
};

// Export all service functions
const invoiceSyncService = {
    syncInvoiceToQBO,
    syncAllInvoicesToQBO,
    getInvoiceSyncStatus,
    getAllInvoicesSyncStatus,
    getSyncStatistics
};

export default invoiceSyncService;